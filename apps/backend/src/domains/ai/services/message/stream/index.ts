import type { Response } from "express";
import type { JSONSchema7 } from "json-schema";
import { v4 as uuidv4 } from "uuid";

import type { AiMessage, AiTool } from "../../../../../providers/ai/index.js";
import { jsonSchema } from "../../../../../providers/ai/index.js";
import { createMcpClient } from "../../../utils/createMcpClient.js";
import { evaluatePrompt } from "../../../utils/evaluatePrompt.js";
import { reshapeMessages } from "../../../utils/reshapeMessages.js";
import { reshapeTools } from "../../../utils/reshapeTools/index.js";
import type { AiServicesDependencies } from "../../dependencies.js";
import type { AiTypes } from "../../index.js";
import { createDocumentTool } from "./tools/createDocument.js";
import { updateDocumentTool } from "./tools/updateDocument.js";

export type StreamPayload = {
  workspaceUuid: string;
  chatUuid: string;
  userUuid: string;
  parts: Array<
    AiTypes.TextPart | AiTypes.ToolCallPart | AiTypes.ToolResultPart
  >;
  attachments: AiTypes.MessageAttachment[];
  uuid: string;
};

export type StreamResult =
  | {
      outcome: "streaming";
      userMessage: AiTypes.Message;
      pipeAssistantStreamToResponse: (response: Response) => void;
    }
  | {
      outcome: "notStreamed";
      reason: "chatNotFound";
    }
  | {
      outcome: "notStreamed";
      reason: "releaseNotFound";
    }
  | {
      outcome: "notStreamed";
      reason: "invalidSystemPrompt";
      errorMessage: string;
    };

export const stream = async (
  dependencies: AiServicesDependencies,
  payload: StreamPayload,
): Promise<StreamResult> => {
  const { repositories, providers } = dependencies;
  const { workspaceUuid, chatUuid, userUuid, parts, attachments, uuid } =
    payload;

  const chat = await repositories.pg.chat.get({
    workspaceUuid,
    uuid: chatUuid,
  });

  if (chat === undefined) {
    return {
      outcome: "notStreamed",
      reason: "chatNotFound",
    };
  }

  const release = await repositories.pg.release.get({
    workspaceUuid,
    uuid: chat.releaseUuid,
  });

  if (release === undefined) {
    return {
      outcome: "notStreamed",
      reason: "releaseNotFound",
    };
  }

  const previousMessages = await repositories.pg.message.list({
    workspaceUuid,
    chatUuid,
  });

  // Register the user message in the database
  const now = new Date();

  const userMessage = await repositories.pg.message.create({
    uuid,
    workspaceUuid,
    agentUuid: chat.agentUuid,
    releaseUuid: chat.releaseUuid,
    chatUuid,
    userUuid,
    type: "user",
    status: "success",
    parts,
    attachments,
    finishedAt: now,
  });

  const reshapedMessages = await reshapeMessages(dependencies, {
    messages: [...previousMessages, userMessage],
  });

  const aiMessages = reshapedMessages.map<AiMessage>((reshapedMessage) => {
    return {
      id: reshapedMessage.id,
      parts: reshapedMessage.parts as AiMessage["parts"],
      role: reshapedMessage.role,
      content: reshapedMessage.content,
      createdAt: reshapedMessage.createdAt,
      experimental_attachments: reshapedMessage.attachments,
    };
  });

  const aiMcpClients = await Promise.all(
    release.mcpClients.map(async (mcpClient) => {
      return createMcpClient(dependencies, { mcpClient });
    }),
  );

  const reshapedTools = await reshapeTools(dependencies, {
    workspaceUuid,
    tools: release.tools,
    resources: release.resources,
  });

  const aiTools = {
    ...reshapedTools.reduce<Record<string, AiTool>>((aiTools, reshapedTool) => {
      return {
        ...aiTools,
        [reshapedTool.slug]: {
          id: <const>`${reshapedTool.source}.${reshapedTool.slug}`,
          description: reshapedTool.description,
          parameters: jsonSchema(reshapedTool.config.jsonSchema as JSONSchema7),
          execute: reshapedTool.execute,
        },
      };
    }, {}),
    ...aiMcpClients.reduce((aiTools, aiMcpClient) => {
      return {
        ...aiTools,
        ...aiMcpClient.tools,
      };
    }, {}),
  };

  const evaluateSystemPromptResult = await evaluatePrompt(dependencies, {
    prompt: release.systemPrompt,
    tools: release.tools,
    resources: release.resources,
  });

  if (evaluateSystemPromptResult.outcome === "notEvaluated") {
    return {
      outcome: "notStreamed",
      reason: "invalidSystemPrompt",
      errorMessage: evaluateSystemPromptResult.errorMessage,
    };
  }

  return {
    outcome: "streaming",
    userMessage,
    pipeAssistantStreamToResponse: (response) => {
      providers.vercelAi.pipeDataStreamToResponse(response, {
        execute: (dataStream) => {
          const streamTextResult = providers.vercelAi.streamText({
            messages: [
              {
                role: "system",
                content: evaluateSystemPromptResult.prompt,
              },
              ...aiMessages,
            ],
            model: providers.vercelAi
              .getProvider(release.provider)
              .languageModel(release.model),
            maxSteps: release.maxSteps,
            temperature: release.temperature,
            tools: {
              ...aiTools,
              createDocument: createDocumentTool(dependencies, {
                workspaceUuid,
                userUuid,
                dataStream,
              }),
              updateDocument: updateDocumentTool(dependencies, {
                workspaceUuid,
                userUuid,
                dataStream,
              }),
            },
            experimental_generateMessageId: uuidv4,
            experimental_transform: [
              providers.vercelAi.smoothStream({
                chunking: "word",
              }),
            ],
            onFinish: async ({ response }) => {
              const appendAiMessages =
                providers.vercelAi.appendResponseMessages({
                  messages: aiMessages,
                  responseMessages: response.messages,
                });

              const assistantAiMessage = appendAiMessages.at(-1);

              if (assistantAiMessage === undefined) {
                return;
              }

              await repositories.pg.message.create({
                uuid: assistantAiMessage.id,
                workspaceUuid,
                agentUuid: chat.agentUuid,
                releaseUuid: chat.releaseUuid,
                chatUuid,
                type: "assistant",
                status: "success",
                parts: assistantAiMessage.parts as AiTypes.MessagePart[],
                attachments: [],
                finishedAt: new Date(),
              });

              await Promise.all(
                aiMcpClients.map(async (aiMcpClient) => {
                  await aiMcpClient.close();
                }),
              );
            },
            onError: async () => {
              await Promise.all(
                aiMcpClients.map(async (aiMcpClient) => {
                  await aiMcpClient.close();
                }),
              );
            },
          });

          streamTextResult.consumeStream();

          streamTextResult.mergeIntoDataStream(dataStream, {
            sendReasoning: true,
          });
        },
        onError: (error) => {
          return error instanceof Error ? error.message : "Unknown error";
        },
      });
    },
  };
};
