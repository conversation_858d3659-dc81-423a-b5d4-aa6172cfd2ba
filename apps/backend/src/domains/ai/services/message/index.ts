import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";
import type { CreatePayload, CreateResult } from "./create.js";
import { create } from "./create.js";
import type {
  CreateFromEventPayload,
  CreateFromEventResult,
} from "./createFromEvent.js";
import { createFromEvent } from "./createFromEvent.js";
import type { GetPayload } from "./get.js";
import { get } from "./get.js";
import type { ListPayload } from "./list.js";
import { list } from "./list.js";
import type { RemovePayload, RemoveResult } from "./remove.js";
import { remove } from "./remove.js";
import type { StreamPayload, StreamResult } from "./stream/index.js";
import { stream } from "./stream/index.js";

export type MessageService = {
  list: (payload: ListPayload) => Promise<AiTypes.Message[]>;
  create: (payload: CreatePayload) => Promise<CreateResult>;
  createFromEvent: (
    payload: CreateFromEventPayload,
  ) => Promise<CreateFromEventResult>;
  get: (payload: GetPayload) => Promise<AiTypes.Message | undefined>;
  stream: (payload: StreamPayload) => Promise<StreamResult>;
  remove: (payload: RemovePayload) => Promise<RemoveResult>;
};

export const buildMessageService = (
  dependencies: AiServicesDependencies,
): MessageService => {
  return {
    list: (payload) => list(dependencies, payload),
    create: (payload) => create(dependencies, payload),
    createFromEvent: (payload) => createFromEvent(dependencies, payload),
    get: (payload) => get(dependencies, payload),
    stream: (payload) => stream(dependencies, payload),
    remove: (payload) => remove(dependencies, payload),
  };
};
