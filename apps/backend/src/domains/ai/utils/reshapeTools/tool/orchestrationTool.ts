import { snakeCase } from "snake-case";

import { OrchestrationUtils } from "../../../../orchestration/index.js";
import type { AiTypes } from "../../../index.js";
import { generateToolJsonSchema } from "../../generateToolJsonSchema.js";
import type { ToolDependencies } from "./dependencies.js";
import type { Tool } from "./tool.js";
import { TOOL_EXECUTE_TIMEOUT } from "./tool.js";

export type CreateToolFromOrchestrationTool = {
  workspaceUuid: string;
  tool: AiTypes.Tool & { kind: "tool" };
};

export const createToolFromOrchestrationTool = async (
  dependencies: ToolDependencies,
  payload: CreateToolFromOrchestrationTool,
): Promise<Tool | undefined> => {
  const { services } = dependencies;
  const { workspaceUuid, tool } = payload;

  if (tool.toolUuid === null) {
    return undefined;
  }

  const orchestrationTool = await services.orchestration.tool.get({
    workspaceUuid,
    uuid: tool.toolUuid,
  });

  if (orchestrationTool === undefined) {
    return undefined;
  }

  const workflow = await services.orchestration.workflow.get({
    workspaceUuid,
    uuid: orchestrationTool.workflowUuid,
  });

  if (workflow === undefined) {
    return undefined;
  }

  const formFields =
    workflow !== undefined &&
    workflow.deployedRelease !== null &&
    workflow.deployedRelease.formFields !== null
      ? workflow.deployedRelease.formFields
      : undefined;

  if (formFields === undefined) {
    return undefined;
  }

  const reshapedToolSlug =
    tool.slug !== null ? tool.slug : snakeCase(tool.name);

  const reshapedToolDescription =
    tool.description !== null
      ? tool.description
      : orchestrationTool.description;

  return {
    source: "tool",
    slug: reshapedToolSlug,
    description:
      reshapedToolDescription !== null ? reshapedToolDescription : tool.name,
    config: {
      jsonSchema: await generateToolJsonSchema(dependencies, {
        jsonSchema:
          OrchestrationUtils.reshapeFormFieldsToJsonSchema(formFields),
        config: tool.config,
      }),
    },
    execute: async (args) => {
      const createRunResult = await services.orchestration.run.create({
        workspaceUuid,
        workflowUuid: workflow.uuid,
        data: args,
      });

      if (createRunResult.outcome === "notCreated") {
        if (createRunResult.reason === "invalidData") {
          throw new Error(createRunResult.errorMessage);
        }

        if (createRunResult.reason === "failedToStart") {
          throw new Error("Failed to start");
        }

        if (createRunResult.reason === "startNodeNotFound") {
          throw new Error("Start node not found");
        }

        if (createRunResult.reason === "workflowNotFound") {
          throw new Error("Unable to retrieve workflow");
        }

        if (createRunResult.reason === "releaseNotFound") {
          throw new Error("Unable to retrieve release");
        }

        throw new Error("Unknown error");
      }

      const { run } = createRunResult;

      const waitUntilRunFinishedResult =
        await services.orchestration.run.waitUntilFinished({
          workspaceUuid: run.workspaceUuid,
          uuid: run.uuid,
          timeoutInMilliseconds: TOOL_EXECUTE_TIMEOUT,
        });

      if (waitUntilRunFinishedResult.outcome === "notFinished") {
        if (waitUntilRunFinishedResult.reason === "runNotFound") {
          throw new Error("Unable to retrieve temporal workflow");
        }

        if (waitUntilRunFinishedResult.reason === "runTimedOut") {
          throw new Error("Execution took too long to process and timed out");
        }

        throw new Error("Unknown error");
      }

      if (waitUntilRunFinishedResult.run.status !== "success") {
        throw new Error(
          waitUntilRunFinishedResult.run.errorMessage !== null
            ? waitUntilRunFinishedResult.run.errorMessage
            : "Failed to execute",
        );
      }

      const result =
        waitUntilRunFinishedResult.runContext["end"] !== null
          ? waitUntilRunFinishedResult.runContext["end"]
          : {};

      return result;
    },
  };
};
