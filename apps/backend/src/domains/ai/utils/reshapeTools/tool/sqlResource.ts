import { snakeCase } from "snake-case";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

import { reshapeToWarehouseKeyword } from "../../../../../utils/warehouse.js";
import {
  type SystemOfRecordIntegrationTypes,
  SystemOfRecordIntegrationUtils,
} from "../../../../systemOfRecordIntegration/index.js";
import type { AiTypes } from "../../../index.js";
import type { ToolDependencies } from "./dependencies.js";
import type { Tool } from "./tool.js";
import {
  TOOL_RESOURCE_DEFAULT_FILTER,
  TOOL_RESOURCE_DEFAULT_LIMIT,
  TOOL_RESOURCE_GUIDELINES_PROMPT,
  TOOL_RESOURCE_SQL_BIGQUERY_PROMPT,
  TOOL_RESOURCE_SQL_SNOWFLAKE_PROMPT,
} from "./tool.js";

export type CreateToolFromSqlResource = {
  workspaceUuid: string;
  resource: AiTypes.Resource;
};

export const createToolFromSqlResource = async (
  dependencies: ToolDependencies,
  payload: CreateToolFromSqlResource,
): Promise<Tool | undefined> => {
  const { services, providers, logger } = dependencies;
  const { workspaceUuid, resource } = payload;

  const reshapedToolSlug =
    resource.slug !== null ? resource.slug : snakeCase(resource.name);

  const model = await services.storage.model.get({
    workspaceUuid,
    uuid: resource.modelUuid,
  });

  if (model === undefined) {
    return undefined;
  }

  const dataset = await services.storage.dataset.get({
    workspaceUuid,
    uuid: model.datasetUuid,
  });

  if (dataset === undefined) {
    return undefined;
  }

  const connector = await services.connection.connector.get({
    workspaceUuid,
    uuid: dataset.connectorUuid,
  });

  if (connector === undefined) {
    return undefined;
  }

  return {
    source: connector.integrationSlug,
    slug: reshapedToolSlug,
    description:
      resource.description !== null
        ? resource.description
        : `Retrieve records from ${connector.name} ${model.name} to answer questions.`,
    config: {
      jsonSchema: {
        type: "object",
        properties: {
          question: {
            title: "Question",
            type: "string",
          },
        },
        required: ["question"],
        additionalProperties: false,
      },
    },
    execute: async (args: { question: string }) => {
      const client = await services.systemOfRecordIntegration.client.get({
        workspaceUuid,
      });

      if (client === undefined) {
        return [];
      }

      const dataset = await services.storage.dataset.get({
        workspaceUuid,
        uuid: model.datasetUuid,
      });

      if (dataset === undefined) {
        return [];
      }

      const models = await services.storage.model.list(
        {
          workspaceUuid,
          datasetUuid: model.datasetUuid,
        },
        { enrich: false },
      );

      const relationships = await services.storage.relationship.list({
        modelUuid: model.uuid,
        workspaceUuid,
      });

      const segments = await services.segmentation.segment.list(
        {
          workspaceUuid,
          modelUuid: model.uuid,
          fromPlay: false,
        },
        { enrich: false },
      );

      const sorModel: SystemOfRecordIntegrationTypes.FilterModel = {
        uuid: model.uuid,
        slug: model.slug,
        datasetSlug: dataset.slug,
      };

      const sorRelationships =
        SystemOfRecordIntegrationUtils.reshapeStorageRelationshipsIntoSorFilterRelationships(
          model.uuid,
          models,
          relationships,
        );

      const sorSegments: SystemOfRecordIntegrationTypes.Segment[] = segments
        .filter((segment) => segment.recordsCount > 0)
        .map((segment) => ({
          uuid: segment.uuid,
          slug: segment.slug,
          name: segment.name,
        }));

      const filteredModelCommand = client.commands.getFilteredModel({
        model: sorModel,
        relationships: sorRelationships,
        segments: sorSegments,
        filter:
          resource.filter !== null
            ? resource.filter
            : TOOL_RESOURCE_DEFAULT_FILTER,
      });

      const tmpTable = client.references.computation.getTable(
        `tmp_${reshapeToWarehouseKeyword(uuidv4())}`,
      );

      await client.createTable(tmpTable, filteredModelCommand, {
        isTemporary: true,
        materialization: "view",
      });

      const tableDdl = await client.getTableDdl(
        client.references.storage.model(dataset.slug, model.slug).getTable(),
      );

      if (tableDdl === undefined) {
        throw new Error("Unable to retrieve DDL");
      }

      const tableDdlMatches = tableDdl.match(/(?<=\().*(?=\))/s);

      if (tableDdlMatches === null || tableDdlMatches[0] === undefined) {
        throw new Error("No DDL matches");
      }

      const tmpTableDdl = `create table ${tmpTable.database}.${tmpTable.schema}.${tmpTable.name} (${tableDdlMatches[0]})`;

      const tmpTableColumnsEnums = await Promise.all(
        model.columns.map(async (column) => {
          if (column.type === "string") {
            return { columnSlug: column.slug, enums: [] };
          }

          const autocompleteResult = await services.storage.column.autocomplete(
            {
              workspaceUuid,
              modelUuid: model.uuid,
              slug: column.slug,
            },
          );

          if (autocompleteResult.outcome === "notAutocompleted") {
            return { columnSlug: column.slug, enums: [] };
          }

          return { columnSlug: column.slug, enums: autocompleteResult.results };
        }),
      );

      const filteredTmpTableColumnsEnums = tmpTableColumnsEnums.filter(
        (columnEnums) => {
          return columnEnums.enums.length > 0;
        },
      );

      const stringifiedTmpTableColumnsEnums = filteredTmpTableColumnsEnums
        .map((columnEnums) => {
          const stringifiedEnums = columnEnums.enums
            .map(({ value }) => {
              return `"${value}"`;
            })
            .join(", ");

          return `${columnEnums.columnSlug}: [${stringifiedEnums}]`;
        })
        .join("\n");

      const prompt =
        client.kind === "snowflake"
          ? TOOL_RESOURCE_SQL_SNOWFLAKE_PROMPT
          : TOOL_RESOURCE_SQL_BIGQUERY_PROMPT;

      let evaluatedPrompt = prompt
        .replaceAll(
          "{{tableName}}",
          `${tmpTable.database}.${tmpTable.schema}.${tmpTable.name}`,
        )
        .replaceAll("{{tableDdl}}", tmpTableDdl)
        .replaceAll("{{tableColumnsEnums}}", stringifiedTmpTableColumnsEnums)
        .replaceAll(
          "{{limit}}",
          `${resource.limit !== null ? resource.limit : TOOL_RESOURCE_DEFAULT_LIMIT}`,
        );

      if (resource.prompt !== null) {
        evaluatedPrompt = evaluatedPrompt + "\n";
        TOOL_RESOURCE_GUIDELINES_PROMPT.replaceAll(
          "{{guidelinesPrompt}}",
          resource.prompt,
        );
      }

      const aiGenerateTextResult = await providers.vercelAi.generateObject({
        model: providers.vercelAi.getProvider("openai").languageModel("gpt-4o"),
        system: evaluatedPrompt,
        prompt: `Generate the query necessary to retrieve the data the user wants: ${args.question}`,
        schema: z.object({
          query: z.string(),
        }),
      });

      logger.info("SQL resource - query generated", {
        question: args.question,
        prompt: evaluatedPrompt,
        query: aiGenerateTextResult.object.query,
      });

      const queryResult = await client.query(aiGenerateTextResult.object.query);

      if (queryResult.outcome === "notQueried") {
        throw new Error(queryResult.errorMessage);
      }

      return { records: queryResult.rows };
    },
  };
};
