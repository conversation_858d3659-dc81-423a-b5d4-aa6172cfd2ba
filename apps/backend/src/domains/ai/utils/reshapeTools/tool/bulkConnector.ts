import { snakeCase } from "snake-case";

import { getDecryptedValuesWithAES } from "../../../../../utils/encryption.js";
import { ConnectionUtils } from "../../../../connection/index.js";
import type { AiTypes } from "../../../index.js";
import { generateToolJsonSchema } from "../../generateToolJsonSchema.js";
import type { ToolDependencies } from "./dependencies.js";
import { type Tool, TOOL_BULK_SUFFIX } from "./tool.js";

export type CreateBulkToolFromConnector = {
  workspaceUuid: string;
  tool: AiTypes.Tool & { kind: "connector" };
};

export const createBulkToolFromConnector = async (
  dependencies: ToolDependencies,
  payload: CreateBulkToolFromConnector,
): Promise<Tool | undefined> => {
  const { services, encryption } = dependencies;
  const { workspaceUuid, tool } = payload;

  if (tool.connectorUuid === null) {
    return undefined;
  }

  const connector = await services.connection.connector.get({
    workspaceUuid,
    uuid: tool.connectorUuid,
  });

  if (connector === undefined) {
    return undefined;
  }

  const integration = await services.connection.integration.get({
    workspaceUuid,
    slug: connector.integrationSlug,
  });

  if (integration === undefined) {
    return undefined;
  }

  const integrationAction = integration.manifest.actions[tool.actionSlug];

  if (integrationAction === undefined) {
    return undefined;
  }

  const reshapedToolSlug =
    tool.slug !== null ? tool.slug : snakeCase(tool.name);

  const reshapedToolDescription =
    tool.description !== null
      ? tool.description
      : integrationAction.description;

  return {
    source: connector.integrationSlug,
    slug: `${reshapedToolSlug}${TOOL_BULK_SUFFIX}`,
    description: `${reshapedToolDescription} (in bulk)`,
    config: {
      jsonSchema: await generateToolJsonSchema(dependencies, {
        jsonSchema: integrationAction.config.jsonSchema,
        config: tool.config,
      }),
    },
    execute: async (args: { items: Record<string, unknown>[] }) => {
      if (connector.useCredits === true) {
        const hasCredits = await services.billing.subscription.hasCredits({
          workspaceUuid,
        });

        if (hasCredits === false) {
          throw new Error("Not enough credits");
        }
      }

      const decryptedConnectorConfig = getDecryptedValuesWithAES(
        connector.config,
        ConnectionUtils.getConnectorEncryptionPassphrase(
          encryption.passphrase,
          connector,
        ),
      );

      const integrationAction = integration.actions[tool.actionSlug];

      if (integrationAction === undefined) {
        throw new Error("Unable to retrieve action");
      }

      const validateResult = await integrationAction.validate({
        config: args,
      });

      if (validateResult.outcome === "notValid") {
        return {
          outcome: "notExecuted",
          errorMessage: validateResult.errorMessage,
          retry: false,
        };
      }

      const executeResult = await integrationAction.execute({
        connector: {
          uuid: connector.uuid,
          workspaceUuid,
          config: decryptedConnectorConfig,
        },
        config: args,
        isDryExecution: false,
      });

      if (executeResult.outcome === "executing") {
        return { outcome: "executing" };
      }

      const {
        data,
        unitsCount = services.connection.integration.getUnitsCount({ data }),
        title,
        iconUrl,
        childIndex = 0,
      } = executeResult;

      let creditsUsedCount: number | undefined = undefined;

      if (connector.useCredits === true) {
        const integrationManifestActionCredits =
          integration.manifest.actions[tool.actionSlug]?.credits;

        if (
          integrationManifestActionCredits !== undefined &&
          unitsCount !== undefined
        ) {
          creditsUsedCount =
            services.connection.integration.getCreditsUsedCount({
              creditsCosts: integrationManifestActionCredits.costs,
              unitsCount,
              config: args,
            });

          await services.billing.usage.create({
            workspaceUuid,
            integrationSlug: connector.integrationSlug,
            connectorUuid: connector.uuid,
            unit: "integration.credits",
            count: creditsUsedCount,
          });
        }
      }

      if (
        integration.manifest.connector.caching !== undefined &&
        integration.manifest.connector.caching.isCompatible === true &&
        connector.cacheTtlMilliseconds !== null
      ) {
        await services.connection.integration.setCachedActionResult({
          workspaceUuid,
          connectorUuid: connector.uuid,
          actionSlug: tool.actionSlug,
          config: args,
          result: {
            data,
            childIndex,
            title,
            iconUrl,
          },
        });
      }

      return data;
    },
  };
};
