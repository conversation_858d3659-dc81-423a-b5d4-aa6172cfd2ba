import { snakeCase } from "snake-case";

import { OrchestrationUtils } from "../../../../orchestration/index.js";
import type { AiTypes } from "../../../index.js";
import { generateToolJsonSchema } from "../../generateToolJsonSchema.js";
import type { ToolDependencies } from "./dependencies.js";
import type { Tool } from "./tool.js";
import { TOOL_BULK_SUFFIX, TOOL_EXECUTE_TIMEOUT } from "./tool.js";

export type CreateBulkToolFromBOrchestrationTool = {
  workspaceUuid: string;
  tool: AiTypes.Tool & { kind: "tool" };
};

export const createBulkToolFromOrchestrationTool = async (
  dependencies: ToolDependencies,
  payload: CreateBulkToolFromBOrchestrationTool,
): Promise<Tool | undefined> => {
  const { services } = dependencies;
  const { workspaceUuid, tool } = payload;

  if (tool.toolUuid === null) {
    return undefined;
  }

  const orchestrationTool = await services.orchestration.tool.get({
    workspaceUuid,
    uuid: tool.toolUuid,
  });

  if (orchestrationTool === undefined) {
    return undefined;
  }

  const workflow = await services.orchestration.workflow.get({
    workspaceUuid,
    uuid: orchestrationTool.workflowUuid,
  });

  if (workflow === undefined) {
    return undefined;
  }

  const formFields =
    workflow !== undefined &&
    workflow.deployedRelease !== null &&
    workflow.deployedRelease.formFields !== null
      ? workflow.deployedRelease.formFields
      : undefined;

  if (formFields === undefined) {
    return undefined;
  }

  const reshapedToolSlug =
    tool.slug !== null ? tool.slug : snakeCase(tool.name);

  const reshapedToolDescription =
    tool.description !== null
      ? tool.description
      : orchestrationTool.description;

  return {
    source: "tool",
    slug: `${reshapedToolSlug}${TOOL_BULK_SUFFIX}`,
    description: `${reshapedToolDescription !== null ? reshapedToolDescription : tool.name} (in bulk)`,
    config: {
      jsonSchema: {
        type: "object",
        properties: {
          items: {
            type: "array",
            items: await generateToolJsonSchema(dependencies, {
              jsonSchema:
                OrchestrationUtils.reshapeFormFieldsToJsonSchema(formFields),
              config: tool.config,
            }),
          },
        },
        required: ["items"],
      },
    },
    execute: async (args: { items: Record<string, unknown>[] }) => {
      const createBatchResult = await services.orchestration.batch.create({
        workspaceUuid,
        workflowUuid: workflow.uuid,
        data: {
          kind: "form",
          records: args.items,
        },
      });

      if (createBatchResult.outcome === "notCreated") {
        if (createBatchResult.reason === "workflowNotFound") {
          throw new Error("Unable to retrieve workflow");
        }

        if (createBatchResult.reason === "dataError") {
          throw new Error(
            createBatchResult.dataError.outcome === "error" &&
            createBatchResult.dataError.reason === "invalidRecords"
              ? createBatchResult.dataError.errors.join(", ")
              : "Invalid data",
          );
        }

        if (createBatchResult.reason === "failedToStart") {
          throw new Error("Failed to start");
        }

        if (createBatchResult.reason === "releaseNotDeployed") {
          throw new Error("Release not deployed");
        }

        throw new Error("Unknown error");
      }

      const { batch } = createBatchResult;

      const waitUntilBatchFinishedResult =
        await services.orchestration.batch.waitUntilFinished({
          workspaceUuid: batch.workspaceUuid,
          uuid: batch.uuid,
          timeoutInMilliseconds: TOOL_EXECUTE_TIMEOUT,
        });

      if (waitUntilBatchFinishedResult.outcome === "notFinished") {
        if (waitUntilBatchFinishedResult.reason === "batchNotFound") {
          throw new Error("Unable to retrieve temporal workflow");
        }

        if (waitUntilBatchFinishedResult.reason === "batchTimedOut") {
          throw new Error("Execution took too long to process and timed out");
        }

        throw new Error("Unknown error");
      }

      if (waitUntilBatchFinishedResult.batch.status !== "success") {
        throw new Error(
          waitUntilBatchFinishedResult.batch.errorMessage !== null
            ? waitUntilBatchFinishedResult.batch.errorMessage
            : "Failed to execute",
        );
      }

      const runs = await services.orchestration.run.list({
        workspaceUuid: batch.workspaceUuid,
        workflowUuid: batch.workflowUuid,
        batchUuid: batch.uuid,
      });

      const results = await Promise.all(
        runs.map(async (run) => {
          const enrichedRun = await services.orchestration.run.get({
            workspaceUuid: run.workspaceUuid,
            uuid: run.uuid,
          });

          if (
            enrichedRun === undefined ||
            enrichedRun.runContext["end"] === null
          ) {
            return {};
          }

          return enrichedRun.runContext["end"];
        }),
      );

      return results;
    },
  };
};
