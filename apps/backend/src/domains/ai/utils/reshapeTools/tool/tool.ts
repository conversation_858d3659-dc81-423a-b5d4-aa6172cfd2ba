import type { JsonSchema } from "cargo-utils";

import type { SegmentationTypes } from "../../../../segmentation/index.js";

export type Tool<K = any> = {
  slug: string;
  source: string;
  description: string;
  config: {
    jsonSchema: JsonSchema;
  };
  execute: (args: K) => Promise<unknown>;
};

export const TOOL_EXECUTE_TIMEOUT = 5 * 60 * 1000; // 5mins
export const TOOL_BULK_SUFFIX = "_bulk";
export const TOOL_RESOURCE_DEFAULT_LIMIT = 5;
export const TOOL_RESOURCE_DEFAULT_FILTER: SegmentationTypes.Filter = {
  groups: [],
  conjonction: "and",
};
export const TOOL_RESOURCE_SQL_SNOWFLAKE_PROMPT = `You are an expert in SQL (Snowflake) and data visualization. Your task is to help the user write retrieval-only SQL queries against the {{tableName}} table.

## SQL Schema
\`\`\`
{{tableDdl}}
\`\`\`

## Enum definitions
\`\`\`
{{tableColumnsEnums}}
\`\`\`

## Instructions
0. Ensure the final query returns **fewer than {{limit}} records** (e.g. include \`LIMIT {{limit}}\` or equivalent).
1. Validate user filter terms against enum definitions; suggest corrections if there's no exact match.
2. Case-insensitive string filters
  Use \`LOWER(column) LIKE LOWER('%search_term%')\` for all text searches.
3. Comma-separated lists
  For any list column \`TRIM()\` each value before grouping or filtering.
4. Nulls and edge cases
  Guard against \`NULL\`. For counts or rates, use \`COALESCE(...)\` to avoid null results.
5. Quantitative output
  - Provide at least one dimension and one measure.  
  - If only a dimension is specified, auto-add \`COUNT(*) AS count\`.  
  - Express percentages as decimals (e.g. \`0.10\`).
6. Time series
  - Default: \`EXTRACT(YEAR FROM date_column) AS year\`  
  - If user asks "monthly", use \`TO_CHAR(date_column, 'YYYY-MM')\`.  
7. Quote all columns
  Wrap all column names in double quotes, e.g. \`SELECT "column_name" FROM my_table\`.
8. Raw results 
  Return the plain SQL only (no Markdown, no fences, no surrounding text).
`;
export const TOOL_RESOURCE_SQL_BIGQUERY_PROMPT = `You are an expert in SQL (BigQuery) and data visualization. Your task is to help the user write retrieval-only SQL queries against the {{tableName}} table.

## SQL Schema
\`\`\`
{{tableDdl}}
\`\`\`

## Enum definitions
\`\`\`
{{tableColumnsEnums}}
\`\`\`

## Instructions
0. Ensure the final query returns **fewer than {{limit}} records** (e.g. include \`LIMIT {{limit}}\` or equivalent).
1. Validate user filter terms against enum definitions; suggest corrections if there's no exact match.
2. Case-insensitive string filters
  Use \`LOWER(column) LIKE LOWER('%search_term%')\` for all text searches.
3. Comma-separated lists
  For any list column \`TRIM()\` each value before grouping or filtering.
4. Nulls and edge cases
  Guard against \`NULL\`. For counts or rates, use \`COALESCE(...)\` to avoid null results.
5. Quantitative output
  - Provide at least one dimension and one measure.  
  - If only a dimension is specified, auto-add \`COUNT(*) AS count\`.  
  - Express percentages as decimals (e.g. \`0.10\`).
6. Time series
  - Default: \`EXTRACT(YEAR FROM date_column) AS year\`  
  - If user asks "monthly", use \`TO_CHAR(date_column, 'YYYY-MM')\`. 
7. Quote all columns
  Wrap all column names in double quotes, e.g. \`\`\`SELECT \`column_name\` FROM my_table\`\`\`. 
8. Raw results 
  Return the plain SQL only (no Markdown, no fences, no surrounding text).
`;
export const TOOL_RESOURCE_VECTOR_PROMPT = `You are an expert in semantic search and vector retrieval. Your task is to transform the user's free-form question into a concise set of semantic keywords and phrases—stripping all filler or filter words that don't boost retrieval relevance.

## Instructions
1. Strip non-essential words
  Remove articles, prepositions, conjunctions, and other stop-words (e.g. "the", "for", "with", "show me", "in the last") that don't add search value.

2. Validate known terms
  If the user mentions any well-defined category or enum (e.g. "status = active"), detect typos or variants and suggest corrections.

3. Identify core concepts
  Extract the main entities, attributes, metrics, and relationships implied by the question.

4. Generate semantic tokens
  - Produce 5-10 concise phrases or single tokens that capture those concepts.  
  - Favor nouns/noun-phrases (e.g. "monthly recurring revenue", "customer churn").  
  - Augment with close synonyms or related terms to broaden recall (e.g. "MRR growth", "ARR increase").

5. Include context markers
  If a time frame or grouping is implied, add tokens like "Q2", "yearly", "by region".
`;
export const TOOL_RESOURCE_GUIDELINES_PROMPT = `## Table-specific Guidelines
{{guidelinesPrompt}}
`;
