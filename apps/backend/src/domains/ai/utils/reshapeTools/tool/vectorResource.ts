import { snakeCase } from "snake-case";

import type { AiTypes } from "../../../index.js";
import type { ToolDependencies } from "./dependencies.js";
import type { Tool } from "./tool.js";
import {
  TOOL_RESOURCE_DEFAULT_FILTER,
  TOOL_RESOURCE_DEFAULT_LIMIT,
  TOOL_RESOURCE_GUIDELINES_PROMPT,
  TOOL_RESOURCE_VECTOR_PROMPT,
} from "./tool.js";

export type CreateToolFromVectorResource = {
  workspaceUuid: string;
  resource: AiTypes.Resource;
};

export const createToolFromVectorResource = async (
  dependencies: ToolDependencies,
  payload: CreateToolFromVectorResource,
): Promise<Tool | undefined> => {
  const { services, providers } = dependencies;
  const { workspaceUuid, resource } = payload;

  const { filter, limit } = resource;

  const reshapedToolSlug =
    resource.slug !== null ? resource.slug : snakeCase(resource.name);

  const model = await services.storage.model.get({
    workspaceUuid,
    uuid: resource.modelUuid,
  });

  if (model === undefined) {
    return undefined;
  }

  const dataset = await services.storage.dataset.get({
    workspaceUuid,
    uuid: model.datasetUuid,
  });

  if (dataset === undefined) {
    return undefined;
  }

  const connector = await services.connection.connector.get({
    workspaceUuid,
    uuid: dataset.connectorUuid,
  });

  if (connector === undefined) {
    return undefined;
  }

  return {
    source: connector.integrationSlug,
    slug: reshapedToolSlug,
    description:
      resource.description !== null
        ? resource.description
        : `Retrieve records from ${connector.name} ${model.name} to answer questions.`,
    config: {
      jsonSchema: {
        type: "object",
        properties: {
          question: {
            title: "Question",
            type: "string",
          },
        },
        required: ["question"],
        additionalProperties: false,
      },
    },
    execute: async (args: { question: string }) => {
      let evaluatedPrompt = TOOL_RESOURCE_VECTOR_PROMPT;

      if (resource.prompt !== null) {
        evaluatedPrompt = evaluatedPrompt + "\n";
        TOOL_RESOURCE_GUIDELINES_PROMPT.replaceAll(
          "{{guidelinesPrompt}}",
          resource.prompt,
        );
      }

      const aiGenerateTextResult = await providers.vercelAi.generateText({
        model: providers.vercelAi.getProvider("openai").languageModel("gpt-4o"),
        system: evaluatedPrompt,
        prompt: `Generate the semantic keywords necessary to retrieve the data the user wants: ${args.question}`,
      });

      const segmentFetchResult = await services.segmentation.segment.fetch({
        workspaceUuid,
        modelUuid: model.uuid,
        filter: filter !== null ? filter : TOOL_RESOURCE_DEFAULT_FILTER,
        limit: limit !== null ? limit : TOOL_RESOURCE_DEFAULT_LIMIT,
        semanticQuery: aiGenerateTextResult.text,
      });

      if (segmentFetchResult.outcome === "notFetched") {
        if (
          segmentFetchResult.reason === "failedToFetchFromClient" ||
          segmentFetchResult.reason === "invalidFilter"
        ) {
          throw new Error(segmentFetchResult.errorMessage);
        }

        throw new Error("Model not found");
      }

      return { records: segmentFetchResult.records };
    },
  };
};
