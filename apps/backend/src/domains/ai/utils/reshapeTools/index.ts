import type { VercelAiClient } from "../../../../providers/ai/index.js";
import type { Logger } from "../../../../providers/logger/index.js";
import type { BillingServices } from "../../../billing/index.js";
import { type ConnectionServices } from "../../../connection/index.js";
import type { ExpressionServices } from "../../../expression/index.js";
import type { OrchestrationServices } from "../../../orchestration/index.js";
import type { SegmentationServices } from "../../../segmentation/index.js";
import type { StorageServices } from "../../../storage/index.js";
import { type SystemOfRecordIntegrationServices } from "../../../systemOfRecordIntegration/index.js";
import type { AiTypes } from "../../index.js";
import { createBulkToolFromConnector } from "./tool/bulkConnector.js";
import { createBulkToolFromOrchestrationTool } from "./tool/bulkOrchestrationTool.js";
import { createToolFromConnector } from "./tool/connector.js";
import { createToolFromOrchestrationTool } from "./tool/orchestrationTool.js";
import { createToolFromSqlResource } from "./tool/sqlResource.js";
import type { Tool } from "./tool/tool.js";
import { createToolFromVectorResource } from "./tool/vectorResource.js";

export type ReshapeToolDependencies = {
  logger: Logger;
  services: {
    billing: BillingServices;
    expression: ExpressionServices;
    orchestration: OrchestrationServices;
    connection: ConnectionServices;
    segmentation: SegmentationServices;
    storage: StorageServices;
    systemOfRecordIntegration: SystemOfRecordIntegrationServices;
  };
  providers: {
    vercelAi: VercelAiClient;
  };
  encryption: {
    passphrase: string;
  };
};

export type ReshapeToolsPayload = {
  workspaceUuid: string;
  tools: AiTypes.Tool[];
  resources: AiTypes.Resource[];
};

export const reshapeTools = async (
  dependencies: ReshapeToolDependencies,
  payload: ReshapeToolsPayload,
): Promise<Tool[]> => {
  const { workspaceUuid, tools, resources } = payload;

  const reshapedTools: Tool[] = [];

  for (const tool of tools) {
    if (tool.kind === "tool") {
      const reshapedTool = await createToolFromOrchestrationTool(dependencies, {
        workspaceUuid,
        tool,
      });

      if (reshapedTool === undefined) {
        continue;
      }

      reshapedTools.push(reshapedTool);

      if (tool.isBulkAllowed === false) {
        continue;
      }

      const reshapedBulkTool = await createBulkToolFromOrchestrationTool(
        dependencies,
        {
          workspaceUuid,
          tool,
        },
      );

      if (reshapedBulkTool === undefined) {
        continue;
      }

      reshapedTools.push(reshapedBulkTool);
    }

    if (tool.kind === "connector") {
      const reshapedTool = await createToolFromConnector(dependencies, {
        workspaceUuid,
        tool,
      });

      if (reshapedTool === undefined) {
        continue;
      }

      reshapedTools.push(reshapedTool);

      if (tool.isBulkAllowed === false) {
        continue;
      }

      const reshapedBulkTool = await createBulkToolFromConnector(dependencies, {
        workspaceUuid,
        tool,
      });

      if (reshapedBulkTool === undefined) {
        continue;
      }

      reshapedTools.push(reshapedBulkTool);
    }
  }

  for (const resource of resources) {
    if (resource.mode === "sql") {
      const reshapedTool = await createToolFromSqlResource(dependencies, {
        workspaceUuid,
        resource,
      });

      if (reshapedTool === undefined) {
        continue;
      }

      reshapedTools.push(reshapedTool);
    }

    if (resource.mode === "vector") {
      const reshapedTool = await createToolFromVectorResource(dependencies, {
        workspaceUuid,
        resource,
      });

      if (reshapedTool === undefined) {
        continue;
      }

      reshapedTools.push(reshapedTool);
    }
  }

  return [
    {
      source: "core",
      slug: "list-available-tools",
      description:
        "List all available tools with their parameters (json schema). Use it for listing all tools or getting information about a given tool.",
      config: {
        jsonSchema: {
          type: "object",
          additionalProperties: false,
          properties: {},
        },
      },
      execute: async () => {
        return JSON.stringify(
          reshapedTools.map((reshapedTool) => {
            return {
              name: reshapedTool.slug,
              description: reshapedTool.description,
              parameters: reshapedTool.config.jsonSchema,
            };
          }),
        );
      },
    },
    ...reshapedTools,
  ];
};
