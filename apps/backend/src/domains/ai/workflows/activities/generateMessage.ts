import type { JSONSchema7 } from "json-schema";

import type { AiMessage, AiTool } from "../../../../providers/ai/index.js";
import { jsonSchema } from "../../../../providers/ai/index.js";
import type { AiTypes } from "../../index.js";
import { createMcpClient } from "../../utils/createMcpClient.js";
import { evaluatePrompt } from "../../utils/evaluatePrompt.js";
import { reshapeMessages } from "../../utils/reshapeMessages.js";
import { reshapeTools } from "../../utils/reshapeTools/index.js";
import type { AiWorkflowActivitiesDependencies } from "./dependencies.js";

export type GenerateMessagePayload = {
  workspaceUuid: string;
  assistantMessageUuid: string;
};
export type GenerateMessageResult =
  | {
      outcome: "generated";
    }
  | {
      outcome: "notGenerated";
      errorMessage: string;
    };

// @aureeaubert - tool or connector should be called in 2 steps
export const generateMessage = async (
  dependencies: AiWorkflowActivitiesDependencies,
  payload: GenerateMessagePayload,
): Promise<GenerateMessageResult> => {
  const { repositories, providers } = dependencies;
  const { workspaceUuid, assistantMessageUuid } = payload;

  const assistantMessage = await repositories.pg.message.get({
    workspaceUuid,
    uuid: assistantMessageUuid,
  });

  if (assistantMessage === undefined) {
    throw new Error("Unable to retrieve assistant message");
  }

  const { chatUuid, agentUuid } = assistantMessage;

  const agent = await repositories.pg.agent.get(
    {
      workspaceUuid,
      uuid: agentUuid,
    },
    { enrich: false },
  );

  if (agent === undefined) {
    throw new Error("Unable to retrieve agent");
  }

  const chat = await repositories.pg.chat.get({
    workspaceUuid,
    uuid: chatUuid,
  });

  if (chat === undefined) {
    throw new Error("Unable to retrieve chat");
  }

  const release = await repositories.pg.release.get({
    workspaceUuid,
    uuid: chat.releaseUuid,
  });

  if (release === undefined) {
    throw new Error("Unable to retrieve release");
  }

  const chatMessages = await repositories.pg.message.list({
    workspaceUuid,
    chatUuid,
  });

  const reshapedMessages = await reshapeMessages(dependencies, {
    messages: chatMessages,
  });

  const aiMessages = reshapedMessages.map<AiMessage>((reshapedMessage) => {
    return {
      id: reshapedMessage.id,
      parts: reshapedMessage.parts as AiMessage["parts"],
      role: reshapedMessage.role,
      content: reshapedMessage.content,
      createdAt: reshapedMessage.createdAt,
      experimental_attachments: reshapedMessage.attachments,
    };
  });

  const aiMcpClients = await Promise.all(
    release.mcpClients.map(async (mcpClient) => {
      return createMcpClient(dependencies, { mcpClient });
    }),
  );

  const reshapedTools = await reshapeTools(dependencies, {
    workspaceUuid,
    tools: release.tools,
    resources: release.resources,
  });

  const aiTools = {
    ...reshapedTools.reduce<Record<string, AiTool>>((aiTools, reshapedTool) => {
      return {
        ...aiTools,
        [reshapedTool.slug]: {
          id: <const>`${reshapedTool.source}.${reshapedTool.slug}`,
          description: reshapedTool.description,
          parameters: jsonSchema(reshapedTool.config.jsonSchema as JSONSchema7),
          execute: reshapedTool.execute,
        },
      };
    }, {}),
    ...aiMcpClients.reduce((aiTools, aiMcpClient) => {
      return {
        ...aiTools,
        ...aiMcpClient.tools,
      };
    }, {}),
  };

  const evaluateSystemPromptResult = await evaluatePrompt(dependencies, {
    prompt: release.systemPrompt,
    tools: release.tools,
    resources: release.resources,
  });

  if (evaluateSystemPromptResult.outcome === "notEvaluated") {
    return {
      outcome: "notGenerated",
      errorMessage: evaluateSystemPromptResult.errorMessage,
    };
  }

  let generatedTextResult: Awaited<
    ReturnType<typeof providers.vercelAi.generateText>
  >;

  try {
    generatedTextResult = await providers.vercelAi.generateText({
      messages: [
        {
          role: "system",
          content: evaluateSystemPromptResult.prompt,
        },
        ...aiMessages,
      ],
      model: providers.vercelAi
        .getProvider(release.provider)
        .languageModel(release.model),
      maxSteps: release.maxSteps,
      temperature: release.temperature,
      tools: aiTools,
    });

    await Promise.all(
      aiMcpClients.map(async (aiMcpClient) => {
        await aiMcpClient.close();
      }),
    );
  } catch (error) {
    await Promise.all(
      aiMcpClients.map(async (aiMcpClient) => {
        await aiMcpClient.close();
      }),
    );

    return {
      outcome: "notGenerated",
      errorMessage: (error as Error).message,
    };
  }

  const { usage } = generatedTextResult;

  const appendAiMessages = providers.vercelAi.appendResponseMessages({
    messages: aiMessages,
    responseMessages: generatedTextResult.response.messages,
  });

  const assistantAiMessage = appendAiMessages.at(-1);

  if (assistantAiMessage === undefined) {
    throw new Error("No ai messages");
  }

  await repositories.pg.message.update({
    uuid: assistantMessageUuid,
    workspaceUuid,
    parts: assistantAiMessage.parts as AiTypes.MessagePart[],
    llmUsage: {
      totalTokensCount: usage.totalTokens,
      promptTokensCount: usage.promptTokens,
      completionTokensCount: usage.completionTokens,
      model: release.model,
    },
  });

  return {
    outcome: "generated",
  };
};
