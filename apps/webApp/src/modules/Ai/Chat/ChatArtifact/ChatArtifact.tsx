import type { UseChatHelpers } from "@ai-sdk/react";
import type { Attachment, UIMessage } from "ai";
import { Ai } from "cargo-api";
import { equals } from "cargo-utils";
import { ApiContext } from "contexts/ApiContext";
import { formatDistance } from "date-fns";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";
import { useMutation, useQuery } from "react-query";

import { ChatInput } from "../ChatInput/ChatInput";
import { useArtifact } from "../hooks/useArtifact";
import { ChatArtifactActions } from "./ChatArtifactActions/ChatArtifactActions";
import { ChatArtifactCloseButton } from "./ChatArtifactCloseButton/ChatArtifactCloseButton";
import { ChatArtifactMessages } from "./ChatArtifactMessages/ChatArtifactMessages";
import { ChatArtifactToolbar } from "./ChatArtifactToolbar/ChatArtifactToolbar";
import { ChatArtifactVersionFooter } from "./ChatArtifactVersionFooter/ChatArtifactVersionFooter";
import { artifactDefinitions } from "./definition";
import { useDebouncedCallback } from "use-debounce";

type Props = {
  agent: Ai.Agent;
  release: Ai.Release;
  chatUuid: string;
  input: string;
  setInput: UseChatHelpers["setInput"];
  status: UseChatHelpers["status"];
  stop: UseChatHelpers["stop"];
  attachments: Attachment[];
  setAttachments: React.Dispatch<React.SetStateAction<Attachment[]>>;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers["setMessages"];
  votes: Ai.Vote[];
  append: UseChatHelpers["append"];
  handleSubmit: UseChatHelpers["handleSubmit"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
};

const PureArtifact: React.FC<Props> = (props) => {
  const {
    agent,
    release,
    chatUuid,
    input,
    setInput,
    handleSubmit,
    status,
    stop,
    attachments,
    setAttachments,
    append,
    messages,
    setMessages,
    reload,
    votes,
    isReadonly,
  } = props;

  const { api } = React.useContext(ApiContext);

  const { artifact, setArtifact, metadata, setMetadata } = useArtifact();

  const parentRef = React.useRef<HTMLDivElement>(null);

  const [documents, setDocuments] = React.useState<Ai.Document[]>([]);

  const documentListState = useQuery(
    Ai.documentKeys.list(release.workspaceUuid, { uuid: artifact.documentId }),
    () => api.ai.document.list({ uuid: artifact.documentId }),
    {
      enabled:
        artifact.documentId !== "init" && artifact.status !== "streaming",
      onSuccess: ({ documents }) => {
        setDocuments(documents);
      },
    },
  );

  const documentCreateState = useMutation(api.ai.document.create);

  const [mode, setMode] = React.useState<"edit" | "diff">("edit");
  const [document, setDocument] = React.useState<Ai.Document | undefined>(
    undefined,
  );
  const [currentVersionIndex, setCurrentVersionIndex] =
    React.useState<number>(0);

  React.useEffect(() => {
    if (documents && documents.length > 0) {
      const [mostRecentDocument] = documents;

      if (mostRecentDocument === undefined) {
        return;
      }

      setDocument(mostRecentDocument);
      setCurrentVersionIndex(documents.length - 1);
      setArtifact((currentArtifact) => ({
        ...currentArtifact,
        content: mostRecentDocument.content,
      }));
    }
  }, [documents, setArtifact]);

  const [isContentDirty, setIsContentDirty] = React.useState(false);

  const [windowWidth, setWindowWidth] = React.useState<number>(
    window.innerWidth,
  );

  React.useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleContentChange = React.useCallback(
    async (updatedContent: string) => {
      if (artifact === undefined) {
        return;
      }

      const [currentDocument] = documents;

      if (
        currentDocument === undefined ||
        currentDocument.content === updatedContent
      ) {
        return;
      }

      const { document } = await documentCreateState.mutateAsync({
        uuid: artifact.documentId,
        title: artifact.title,
        content: updatedContent,
        kind: artifact.kind as Ai.DocumentKind,
      });

      setDocuments([document, ...documents]);
    },
    [artifact, documentCreateState, documents],
  );

  const debouncedHandleContentChange = useDebouncedCallback(
    handleContentChange,
    2000,
  );

  const saveContent = React.useCallback(
    (updatedContent: string, debounce: boolean) => {
      if (document && updatedContent !== document.content) {
        setIsContentDirty(true);

        if (debounce) {
          debouncedHandleContentChange(updatedContent);
        } else {
          handleContentChange(updatedContent);
        }
      }
    },
    [document, debouncedHandleContentChange, handleContentChange],
  );

  const getDocumentContentById = React.useCallback(
    (index: number) => {
      if (documents === undefined || documents[index] === undefined) {
        return "";
      }

      return documents[index].content;
    },
    [documents],
  );

  const handleVersionChange = (type: "next" | "prev" | "toggle" | "latest") => {
    if (!documents) return;

    if (type === "latest") {
      setCurrentVersionIndex(0);
      setMode("edit");
    }

    if (type === "toggle") {
      setMode((mode) => (mode === "edit" ? "diff" : "edit"));
    }

    if (type === "prev") {
      if (currentVersionIndex < documents.length - 1) {
        setCurrentVersionIndex((index) => index + 1);
      }
    } else if (type === "next") {
      if (currentVersionIndex > 0) {
        setCurrentVersionIndex((index) => index - 1);
      }
    }
  };

  const [isToolbarVisible, setIsToolbarVisible] =
    React.useState<boolean>(false);

  const parentRefPosition = React.useMemo(() => {
    if (parentRef.current === null) {
      return {
        top: 0,
        left: 0,
        width: 0,
        height: 0,
        right: 0,
        bottom: 0,
        x: 0,
        y: 0,
      };
    }

    const rect = parentRef.current.getBoundingClientRect();

    return {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height,
      right: rect.right,
      bottom: rect.bottom,
      x: rect.x,
      y: rect.y,
    };
  }, []);

  /*
   * NOTE: if there are no documents, or if
   * the documents are being fetched, then
   * we mark it as the current version.
   */

  const isCurrentVersion =
    documents.length > 0 ? currentVersionIndex === 0 : true;

  const artifactDefinition = React.useMemo(() => {
    return artifactDefinitions.find(
      (definition) => definition.kind === artifact.kind,
    );
  }, [artifact.kind]);

  if (artifactDefinition === undefined) {
    throw new Error("Artifact definition not found!");
  }

  React.useEffect(() => {
    if (artifact.documentId === "init") {
      return;
    }

    if (artifactDefinition.initialize) {
      artifactDefinition.initialize({
        documentId: artifact.documentId,
        setMetadata,
      });
    }
  }, [artifact.documentId, artifactDefinition, setMetadata]);

  return (
    <div ref={parentRef} className="h-0 w-full">
      <AnimatePresence>
        {artifact.isVisible === true ? (
          <motion.div
            className="z-50 fixed left-0 top-0 flex h-dvh w-dvw flex-row"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0, transition: { delay: 0.4 } }}
          >
            <motion.div
              className="fixed h-dvh"
              initial={{
                width: 0,
                right: 0,
              }}
              animate={{ width: windowWidth, right: 0 }}
              exit={{
                width: 0,
                right: 0,
              }}
            />

            <motion.div
              className="relative h-dvh w-[400px] shrink-0"
              initial={{ opacity: 0, x: 10, scale: 1 }}
              animate={{
                opacity: 1,
                x: 0,
                scale: 1,
                transition: {
                  delay: 0.2,
                  type: "spring",
                  stiffness: 200,
                  damping: 30,
                },
              }}
              exit={{
                opacity: 0,
                x: 0,
                scale: 1,
                transition: { duration: 0 },
              }}
            >
              <AnimatePresence>
                {isCurrentVersion === false ? (
                  <motion.div
                    className="z-50 absolute left-0 top-0 h-dvh w-[400px] bg-zinc-900/50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  />
                ) : null}
              </AnimatePresence>

              <div className="flex h-full flex-col items-center justify-between">
                <ChatArtifactMessages
                  agent={agent}
                  release={release}
                  chatUuid={chatUuid}
                  status={status}
                  votes={votes}
                  messages={messages}
                  setMessages={setMessages}
                  reload={reload}
                  isReadonly={isReadonly}
                  artifactStatus={artifact.status}
                />

                <form className="relative flex w-full flex-row items-end gap-2 px-4 pb-4">
                  <ChatInput
                    release={release}
                    input={input}
                    setInput={setInput}
                    handleSubmit={handleSubmit}
                    status={status}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messages}
                    append={append}
                    setMessages={setMessages}
                  />
                </form>
              </div>
            </motion.div>

            <motion.div
              className="fixed flex h-dvh flex-col overflow-y-scroll border-l border-neutral-300/50 dark:border-neutral-700/50"
              initial={{
                opacity: 1,
                x: artifact.boundingBox.left,
                y: artifact.boundingBox.top,
                height: artifact.boundingBox.height,
                width: artifact.boundingBox.width,
                borderRadius: 50,
              }}
              animate={{
                opacity: 1,
                x: 0,
                y: 0,
                height: "calc(100dvh)",
                width: "calc(100dvw)",
                borderRadius: 0,
                transition: {
                  delay: 0,
                  type: "spring",
                  stiffness: 200,
                  damping: 30,
                  duration: 5000,
                },
              }}
              exit={{
                opacity: 0,
                scale: 0.5,
                transition: {
                  delay: 0.1,
                  type: "spring",
                  stiffness: 600,
                  damping: 30,
                },
              }}
            >
              <div className="flex flex-row items-start justify-between p-2">
                <div className="flex flex-row items-start gap-4">
                  <ChatArtifactCloseButton />

                  <div className="flex flex-col">
                    <div className="font-medium">{artifact.title}</div>

                    {isContentDirty === true ? (
                      <div className="text-sm">Saving changes...</div>
                    ) : document ? (
                      <div className="text-sm">
                        {`Updated ${formatDistance(
                          new Date(document.createdAt),
                          new Date(),
                          {
                            addSuffix: true,
                          },
                        )}`}
                      </div>
                    ) : (
                      <div className="mt-2 h-3 w-32 animate-pulse rounded-md" />
                    )}
                  </div>
                </div>

                <ChatArtifactActions
                  artifact={artifact}
                  currentVersionIndex={currentVersionIndex}
                  handleVersionChange={handleVersionChange}
                  isCurrentVersion={isCurrentVersion}
                  mode={mode}
                  metadata={metadata}
                  setMetadata={setMetadata}
                />
              </div>

              <div className="h-full !max-w-full items-center overflow-y-scroll">
                <artifactDefinition.content
                  title={artifact.title}
                  content={
                    isCurrentVersion === true
                      ? artifact.content
                      : getDocumentContentById(currentVersionIndex)
                  }
                  mode={mode}
                  status={artifact.status}
                  currentVersionIndex={currentVersionIndex}
                  suggestions={[]}
                  onSaveContent={saveContent}
                  isInline={false}
                  isCurrentVersion={isCurrentVersion}
                  getDocumentContentById={getDocumentContentById}
                  isLoading={documentListState.isLoading}
                  metadata={metadata}
                  setMetadata={setMetadata}
                />

                <AnimatePresence>
                  {isCurrentVersion === true ? (
                    <ChatArtifactToolbar
                      isToolbarVisible={isToolbarVisible}
                      setIsToolbarVisible={setIsToolbarVisible}
                      append={append}
                      status={status}
                      stop={stop}
                      setMessages={setMessages}
                      artifactKind={artifact.kind}
                    />
                  ) : null}
                </AnimatePresence>
              </div>

              <AnimatePresence>
                {isCurrentVersion === false ? (
                  <ChatArtifactVersionFooter
                    currentVersionIndex={currentVersionIndex}
                    documents={documents}
                    handleVersionChange={handleVersionChange}
                  />
                ) : null}
              </AnimatePresence>
            </motion.div>
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
};

export const Artifact = React.memo(PureArtifact, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (equals(prevProps.votes, nextProps.votes) === false) return false;
  if (prevProps.input !== nextProps.input) return false;
  if (equals(prevProps.messages, nextProps.messages) === false) return false;

  return true;
});
