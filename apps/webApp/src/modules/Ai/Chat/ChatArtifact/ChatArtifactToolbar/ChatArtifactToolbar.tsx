import type { UseChatHelpers } from "@ai-sdk/react";
import StopIcon from "@untitled-ui/icons-react/build/esm/Stop";
import { useClickOutside } from "cargo-react-utils";
import { motion } from "framer-motion";
import React from "react";

import { artifactDefinitions } from "../definition";
import { ChatArtifactReadingLevelSelector } from "./ChatArtifactToolbarReadingLevelSelector/ChatArtifactToolbarReadingLevelSelector";
import { Tools } from "./ChatArtifactToolbarTools/ChatArtifactToolbarTools";

type Props = {
  isToolbarVisible: boolean;
  setIsToolbarVisible: React.Dispatch<React.SetStateAction<boolean>>;
  status: UseChatHelpers["status"];
  append: UseChatHelpers["append"];
  stop: UseChatHelpers["stop"];
  setMessages: UseChatHelpers["setMessages"];
  artifactKind: string;
};

const PureChatArtifactToolbar: React.FC<Props> = (props) => {
  const {
    isToolbarVisible,
    setIsToolbarVisible,
    append,
    status,
    stop,
    setMessages,
    artifactKind,
  } = props;

  const toolbarRef = React.useRef<HTMLDivElement>(null);
  const timeoutRef = React.useRef<ReturnType<typeof setTimeout>>();

  const [selectedTool, setSelectedTool] = React.useState<string | undefined>(
    undefined,
  );
  const [isAnimating, setIsAnimating] = React.useState(false);

  useClickOutside(toolbarRef, () => {
    setIsToolbarVisible(false);
    setSelectedTool(undefined);
  });

  const startCloseTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setSelectedTool(undefined);
      setIsToolbarVisible(false);
    }, 2000);
  };

  const cancelCloseTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  React.useEffect(() => {
    if (status === "streaming") {
      setIsToolbarVisible(false);
    }
  }, [status, setIsToolbarVisible]);

  const artifactDefinition = artifactDefinitions.find(
    (definition) => definition.kind === artifactKind,
  );

  if (!artifactDefinition) {
    throw new Error("Artifact definition not found!");
  }

  const toolsByArtifactKind = artifactDefinition.toolbar;

  if (toolsByArtifactKind.length === 0) {
    return null;
  }

  return (
    <motion.div
      className="absolute bottom-6 right-6 flex cursor-pointer flex-col justify-end rounded-full border p-1.5 shadow-lg"
      initial={{ opacity: 0, y: -20, scale: 1 }}
      animate={
        isToolbarVisible
          ? selectedTool === "adjust-reading-level"
            ? {
                opacity: 1,
                y: 0,
                height: 6 * 43,
                transition: { delay: 0 },
                scale: 0.95,
              }
            : {
                opacity: 1,
                y: 0,
                height: toolsByArtifactKind.length * 50,
                transition: { delay: 0 },
                scale: 1,
              }
          : { opacity: 1, y: 0, height: 54, transition: { delay: 0 } }
      }
      exit={{ opacity: 0, y: -20, transition: { duration: 0.1 } }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      onHoverStart={() => {
        if (status === "streaming") return;

        cancelCloseTimer();
        setIsToolbarVisible(true);
      }}
      onHoverEnd={() => {
        if (status === "streaming") return;

        startCloseTimer();
      }}
      onAnimationStart={() => {
        setIsAnimating(true);
      }}
      onAnimationComplete={() => {
        setIsAnimating(false);
      }}
      ref={toolbarRef}
    >
      {status === "streaming" ? (
        <motion.div
          key="stop-icon"
          initial={{ scale: 1 }}
          animate={{ scale: 1.4 }}
          exit={{ scale: 1 }}
          className="p-3"
          onClick={() => {
            stop();
            setMessages((messages) => messages);
          }}
        >
          <StopIcon />
        </motion.div>
      ) : selectedTool === "adjust-reading-level" ? (
        <ChatArtifactReadingLevelSelector
          key="reading-level-selector"
          append={append}
          setSelectedTool={setSelectedTool}
          isAnimating={isAnimating}
        />
      ) : (
        <Tools
          key="tools"
          append={append}
          isAnimating={isAnimating}
          isToolbarVisible={isToolbarVisible}
          selectedTool={selectedTool}
          setIsToolbarVisible={setIsToolbarVisible}
          setSelectedTool={setSelectedTool}
          tools={toolsByArtifactKind}
        />
      )}
    </motion.div>
  );
};

export const ChatArtifactToolbar = React.memo(
  PureChatArtifactToolbar,
  (prevProps, nextProps) => {
    if (prevProps.status !== nextProps.status) return false;
    if (prevProps.isToolbarVisible !== nextProps.isToolbarVisible) return false;
    if (prevProps.artifactKind !== nextProps.artifactKind) return false;

    return true;
  },
);
