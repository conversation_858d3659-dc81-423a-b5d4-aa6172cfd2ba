import type { UseChatHelpers } from "@ai-sdk/react";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";

import type { ArtifactToolbarItem } from "../../definition/definition";
import { ChatArtifactToolbarTool } from "../ChatArtifactToolbarTool/ChatArtifactToolbarTool";

type Props = {
  isToolbarVisible: boolean;
  selectedTool?: string;
  setSelectedTool: React.Dispatch<React.SetStateAction<string | undefined>>;
  append: UseChatHelpers["append"];
  isAnimating: boolean;
  setIsToolbarVisible: React.Dispatch<React.SetStateAction<boolean>>;
  tools: ArtifactToolbarItem[];
};

export const Tools: React.FC<Props> = (props) => {
  const {
    isToolbarVisible,
    selectedTool,
    setSelectedTool,
    append,
    isAnimating,
    setIsToolbarVisible,
    tools,
  } = props;

  const [primaryTool, ...secondaryTools] = tools;

  if (primaryTool === undefined) {
    return null;
  }

  return (
    <motion.div
      className="flex flex-col gap-1.5"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
    >
      <AnimatePresence>
        {isToolbarVisible === true
          ? secondaryTools.map((secondaryTool) => (
              <ChatArtifactToolbarTool
                key={secondaryTool.description}
                description={secondaryTool.description}
                icon={secondaryTool.icon}
                selectedTool={selectedTool}
                setSelectedTool={setSelectedTool}
                append={append}
                isAnimating={isAnimating}
                onClick={secondaryTool.onClick}
              />
            ))
          : null}
      </AnimatePresence>

      <ChatArtifactToolbarTool
        description={primaryTool.description}
        icon={primaryTool.icon}
        selectedTool={selectedTool}
        setSelectedTool={setSelectedTool}
        isToolbarVisible={isToolbarVisible}
        setIsToolbarVisible={setIsToolbarVisible}
        append={append}
        isAnimating={isAnimating}
        onClick={primaryTool.onClick}
      />
    </motion.div>
  );
};
