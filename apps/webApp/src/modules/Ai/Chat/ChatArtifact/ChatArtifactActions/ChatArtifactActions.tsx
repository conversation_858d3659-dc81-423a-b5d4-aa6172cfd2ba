import { Button } from "cargo-components/Button";
import { LazyTooltip } from "components/LazyTooltip";
import { NotificationContext } from "contexts/NotificationContext";
import React, { useState } from "react";

import { artifactDefinitions } from "../definition";
import type { Artifact, ArtifactActionContext } from "../definition/definition";

type Props = {
  artifact: Artifact;
  handleVersionChange: (type: "next" | "prev" | "toggle" | "latest") => void;
  currentVersionIndex: number;
  isCurrentVersion: boolean;
  mode: "edit" | "diff";
  metadata: unknown;
  setMetadata: React.Dispatch<React.SetStateAction<unknown>>;
};

const PureChatArtifactActions: React.FC<Props> = (props) => {
  const {
    artifact,
    handleVersionChange,
    currentVersionIndex,
    isCurrentVersion,
    mode,
    metadata,
    setMetadata,
  } = props;

  const { addNotification } = React.useContext(NotificationContext);

  const [isLoading, setIsLoading] = useState(false);

  const artifactDefinition = React.useMemo(() => {
    return artifactDefinitions.find(
      (definition) => definition.kind === artifact.kind,
    );
  }, [artifact.kind]);

  if (artifactDefinition === undefined) {
    return null;
  }

  const actionContext: ArtifactActionContext = {
    content: artifact.content,
    handleVersionChange,
    currentVersionIndex,
    isCurrentVersion,
    mode,
    metadata,
    setMetadata,
  };

  return (
    <div className="flex flex-row space-x-2">
      {artifactDefinition.actions.map((action) => (
        <LazyTooltip key={action.description} text={action.description}>
          <Button
            type="secondary"
            onClick={async () => {
              setIsLoading(true);

              try {
                await Promise.resolve(action.onClick(actionContext));
              } catch {
                addNotification({
                  title: "Failed to execute action",
                  type: "error",
                });
              } finally {
                setIsLoading(false);
              }
            }}
            isLoading={isLoading}
            isDisabled={
              artifact.status === "streaming"
                ? true
                : action.isDisabled !== undefined
                  ? action.isDisabled(actionContext)
                  : false
            }
            icon={action.icon}
            text={action.label}
          />
        </LazyTooltip>
      ))}
    </div>
  );
};

export const ChatArtifactActions = React.memo(
  PureChatArtifactActions,
  (prevProps, nextProps) => {
    if (prevProps.artifact.status !== nextProps.artifact.status) return false;
    if (prevProps.currentVersionIndex !== nextProps.currentVersionIndex)
      return false;
    if (prevProps.isCurrentVersion !== nextProps.isCurrentVersion) return false;
    if (prevProps.artifact.content !== nextProps.artifact.content) return false;

    return true;
  },
);
