import ClockRewind from "@untitled-ui/icons-react/build/esm/ClockRewind";
import Copy02 from "@untitled-ui/icons-react/build/esm/Copy02";
import CornerDownLeft from "@untitled-ui/icons-react/build/esm/CornerDownLeft";
import CornerDownRight from "@untitled-ui/icons-react/build/esm/CornerDownRight";
import MessageCircle01 from "@untitled-ui/icons-react/build/esm/MessageCircle01";
import Pencil01 from "@untitled-ui/icons-react/build/esm/Pencil01";
import type { Ai } from "cargo-api";

import type { ArtifactDefinition } from "../definition";
import { DiffView } from "./DiffView";
import { Editor } from "./Editor";
import { Skeleton } from "./Skeleton";

type Metadata = {
  suggestions: Ai.DocumentSuggestion[];
};

export const buildTextArtifactDefinition = (): ArtifactDefinition<
  "text",
  Metadata
> => {
  return {
    kind: "text",
    description: "Useful for text content, like drafting essays and emails.",
    initialize: async ({ setMetadata }) => {
      // TODO: implement suggestions
      /* 
        const suggestions = await getSuggestions({ documentId });
      */

      setMetadata({ suggestions: [] });
    },
    onStreamPart: ({ streamPart, setMetadata, setArtifact }) => {
      if (streamPart.type === "suggestion") {
        setMetadata((metadata) => {
          return {
            suggestions: [
              ...metadata.suggestions,
              streamPart.content as unknown as Ai.DocumentSuggestion,
            ],
          };
        });
      }

      if (streamPart.type === "text-delta") {
        setArtifact((draftArtifact) => {
          return {
            ...draftArtifact,
            content: draftArtifact.content + (streamPart.content as string),
            isVisible:
              draftArtifact.status === "streaming" &&
              draftArtifact.content.length > 400 &&
              draftArtifact.content.length < 450
                ? true
                : draftArtifact.isVisible,
            status: "streaming",
          };
        });
      }
    },
    content: ({
      mode,
      status,
      content,
      isCurrentVersion,
      currentVersionIndex,
      onSaveContent,
      getDocumentContentById,
      isLoading,
      metadata,
    }) => {
      if (isLoading === true) {
        return <Skeleton />;
      }

      if (mode === "diff") {
        const oldContent = getDocumentContentById(currentVersionIndex - 1);
        const newContent = getDocumentContentById(currentVersionIndex);

        return <DiffView oldContent={oldContent} newContent={newContent} />;
      }

      return (
        <>
          <div className="flex flex-row px-4 py-8 md:p-20">
            <Editor
              content={content}
              suggestions={metadata ? metadata.suggestions : []}
              isCurrentVersion={isCurrentVersion}
              currentVersionIndex={currentVersionIndex}
              status={status}
              onSaveContent={onSaveContent}
            />

            {metadata &&
            metadata.suggestions &&
            metadata.suggestions.length > 0 ? (
              <div className="h-dvh w-12 shrink-0 md:hidden" />
            ) : null}
          </div>
        </>
      );
    },
    actions: [
      {
        icon: <ClockRewind className="size-4" />,
        description: "View changes",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("toggle");
        },
        isDisabled: ({ currentVersionIndex }) => {
          if (currentVersionIndex === 0) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <CornerDownLeft className="size-4" />,
        description: "View Previous version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("prev");
        },
        isDisabled: ({ currentVersionIndex }) => {
          if (currentVersionIndex === 0) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <CornerDownRight className="size-4" />,
        description: "View Next version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("next");
        },
        isDisabled: ({ isCurrentVersion }) => {
          if (isCurrentVersion) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <Copy02 className="size-4" />,
        description: "Copy to clipboard",
        onClick: ({ content }) => {
          navigator.clipboard.writeText(content);
        },
      },
    ],
    toolbar: [
      {
        icon: <Pencil01 />,
        description: "Add final polish",
        onClick: ({ appendMessage }) => {
          appendMessage({
            role: "user",
            content:
              "Please add final polish and check for grammar, add section titles for better structure, and ensure everything reads smoothly.",
          });
        },
      },
      {
        icon: <MessageCircle01 className="size-4" />,
        description: "Request suggestions",
        onClick: ({ appendMessage }) => {
          appendMessage({
            role: "user",
            content:
              "Please add suggestions you have that could improve the writing.",
          });
        },
      },
    ],
  };
};
