import type { UseChatHelpers } from "@ai-sdk/react";
import type { Ai } from "cargo-api";
import type React from "react";

export type DataStreamDelta = {
  type:
    | "text-delta"
    | "code-delta"
    | "sheet-delta"
    | "image-delta"
    | "title"
    | "id"
    | "suggestion"
    | "clear"
    | "finish"
    | "kind";
  content: string;
};

export type Artifact = {
  title: string;
  documentId: string;
  kind: string;
  content: string;
  isVisible: boolean;
  status: "streaming" | "idle";
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
};

export type ArtifactActionContext<M = any> = {
  content: string;
  handleVersionChange: (type: "next" | "prev" | "toggle" | "latest") => void;
  currentVersionIndex: number;
  isCurrentVersion: boolean;
  mode: "edit" | "diff";
  metadata: M;
  setMetadata: React.Dispatch<React.SetStateAction<M>>;
};

type ArtifactAction<M = any> = {
  icon: React.ReactNode;
  label?: string;
  description: string;
  onClick: (context: ArtifactActionContext<M>) => Promise<void> | void;
  isDisabled?: (context: ArtifactActionContext<M>) => boolean;
};

export type ArtifactToolbarContext = {
  appendMessage: UseChatHelpers["append"];
};

export type ArtifactToolbarItem = {
  description: string;
  icon: React.ReactNode;
  onClick: (context: ArtifactToolbarContext) => void;
};

type ArtifactContent<M = any> = {
  title: string;
  content: string;
  mode: "edit" | "diff";
  isCurrentVersion: boolean;
  currentVersionIndex: number;
  status: "streaming" | "idle";
  suggestions: Ai.DocumentSuggestion[];
  onSaveContent: (updatedContent: string, debounce: boolean) => void;
  isInline: boolean;
  getDocumentContentById: (index: number) => string;
  isLoading: boolean;
  metadata: M;
  setMetadata: React.Dispatch<React.SetStateAction<M>>;
};

type InitializeParameters<M = any> = {
  documentId: string;
  setMetadata: React.Dispatch<React.SetStateAction<M>>;
};

export type ArtifactDefinition<T extends string, M = any> = {
  kind: T;
  description: string;
  content: React.ComponentType<ArtifactContent<M>>;
  actions: Array<ArtifactAction<M>>;
  toolbar: ArtifactToolbarItem[];
  initialize: (parameters: InitializeParameters) => void;
  onStreamPart: (args: {
    setMetadata: React.Dispatch<React.SetStateAction<M>>;
    setArtifact: React.Dispatch<React.SetStateAction<Artifact>>;
    streamPart: DataStreamDelta;
  }) => void;
};
