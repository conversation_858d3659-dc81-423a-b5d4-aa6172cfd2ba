"use client";

export const Skeleton: React.FC = () => {
  return (
    <div className="flex w-full flex-col gap-4">
      <div className="h-12 w-1/2 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
      <div className="h-5 w-full animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
      <div className="h-5 w-full animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
      <div className="h-5 w-1/3 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
      <div className="h-5 w-52 animate-pulse rounded-lg bg-transparent" />
      <div className="h-8 w-52 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
      <div className="h-5 w-2/3 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
    </div>
  );
};
