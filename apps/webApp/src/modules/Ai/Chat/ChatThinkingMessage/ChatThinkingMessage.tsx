import type { <PERSON> } from "cargo-api";
import { motion } from "framer-motion";
import { AgentIcon } from "modules/Ai/AgentIcon";
import React from "react";

type Props = {
  agent: Ai.Agent;
};

export const ChatThinkingMessage: React.FC<Props> = (props) => {
  const { agent } = props;

  return (
    <motion.div
      className="group/message mx-auto min-h-96 w-full max-w-3xl px-4"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
    >
      <div className="flex w-full space-x-4 rounded-xl group-data-[role=user]/message:ml-auto group-data-[role=user]/message:w-fit group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:px-3 group-data-[role=user]/message:py-2">
        <AgentIcon agent={agent} />

        <div className="flex w-full">Hmm...</div>
      </div>
    </motion.div>
  );
};
