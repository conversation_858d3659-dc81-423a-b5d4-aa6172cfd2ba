/* eslint-disable @typescript-eslint/no-explicit-any */

import type { AiTypes } from "cargo-domain-types";
import type { RecursiveNonNull } from "cargo-utils";
import { z } from "zod";
import type { StateCreator } from "zustand";

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace Ai {
  export type Agent = RecursiveNonNull<AiTypes.Agent>;
  export type AgentIcon = RecursiveNonNull<AiTypes.AgentIcon>;
  export type AgentDeployment = RecursiveNonNull<AiTypes.AgentDeployment>;
  export type AgentCategory = RecursiveNonNull<AiTypes.AgentCategory>;
  export type Release = RecursiveNonNull<AiTypes.Release>;
  export type ReleaseProvider = RecursiveNonNull<AiTypes.ReleaseProvider>;
  export type McpServer = RecursiveNonNull<AiTypes.McpServer>;
  export type Tool = RecursiveNonNull<AiTypes.Tool>;
  export type Resource = RecursiveNonNull<AiTypes.Resource>;
  export type Chat = RecursiveNonNull<AiTypes.Chat>;
  export type ChatTrigger = RecursiveNonNull<AiTypes.ChatTrigger>;
  export type Message = RecursiveNonNull<AiTypes.Message>;
  export type Vote = RecursiveNonNull<AiTypes.Vote>;
  export type Document = RecursiveNonNull<AiTypes.Document>;
  export type DocumentKind = RecursiveNonNull<AiTypes.DocumentKind>;
  export type DocumentSuggestion = RecursiveNonNull<AiTypes.DocumentSuggestion>;

  // eslint-disable-next-line @typescript-eslint/no-namespace
  export namespace Api {
    export type GetAgentResult = {
      agent: Agent;
    };

    export type CreateAgentPayload = {
      name: string;
      description?: string;
      icon: AiTypes.AgentIcon;
      deployments: AiTypes.AgentDeployment[];
      folderUuid?: string;
    };

    export type CreateAgentResult = {
      agent: Agent;
    };

    export type AllAgentsResult = {
      agents: Agent[];
    };

    export type UpdateAgentPayload = {
      uuid: string;
      name?: string;
      icon?: AiTypes.AgentIcon;
      description?: string | null;
      deployments?: AiTypes.AgentDeployment[];
      folderUuid?: string;
    };

    export const zodUpdateAgentErrorReason = z.enum(["agentNotFound"]);

    export type UpdateAgentResult = {
      agent: Agent;
    };

    export const zodRemoveAgentErrorReason = z.enum(["agentNotFound"]);

    export type GetReleaseResult = {
      release: Release;
    };

    export type ListReleasesPayload = {
      agentUuid?: string;
      userUuid?: string;
      statuses?: AiTypes.ReleaseStatus[];
      limit?: number;
      offset?: number;
    };

    export type ListReleasesResult = {
      releases: Release[];
    };

    export type GetDraftReleasePayload = {
      agentUuid: string;
    };

    export type GetDraftReleaseResult = {
      release: Release;
    };

    export type UpdateDraftReleasePayload = {
      agentUuid: string;
      parentUuid?: string;
      tools?: AiTypes.Tool[];
      mcpClients?: AiTypes.ReleaseMcpClient[];
      resources?: AiTypes.Resource[];
      suggestedActions?: AiTypes.ReleaseSuggestedAction[];
      systemPrompt?: string;
      temperature?: number;
      maxSteps?: number;
      provider?: AiTypes.ReleaseProvider;
      model?: string;
    };

    export const zodUpdateDraftReleaseErrorReason = z.enum([
      "draftReleaseNotFound",
      "invalidParent",
    ]);

    export type UpdateDraftReleaseResult = {
      release: Release;
    };

    export type DeployDraftReleasePayload = {
      agentUuid: string;
      tools: AiTypes.Tool[];
      mcpClients: AiTypes.ReleaseMcpClient[];
      resources: AiTypes.Resource[];
      suggestedActions: AiTypes.ReleaseSuggestedAction[];
      systemPrompt?: string;
      temperature?: number;
      maxSteps?: number;
      provider?: AiTypes.ReleaseProvider;
      model?: string;
      version: string;
      description?: string;
    };

    export const zodDeployDraftReleaseErrorReason = z.enum([
      "agentNotFound",
      "draftReleaseNotFound",
      "duplicatedReleaseVersion",
    ]);

    export type DeployDraftReleaseResult = {
      release: Release;
    };

    export type GetChatResult = {
      chat: Chat;
    };

    export const zodGetChatErrorReason = z.enum(["chatNotFound"]);

    export type CreateChatPayload = {
      agentUuid: string;
      releaseUuid?: string;
      name?: string;
      triggerType?: AiTypes.ChatTrigger["type"];
    };

    export type CreateChatResult = {
      chat: Chat;
    };

    export const zodCreateChatErrorReason = z.enum([
      "agentNotFound",
      "releaseNotFound",
    ]);

    export type UpdateChatPayload = {
      uuid: string;
      name?: string | null;
    };

    export type UpdateChatResult = {
      chat: Chat;
    };

    export const zodUpdateChatErrorReason = z.enum(["chatNotFound"]);

    export type RemoveChatPayload = {
      uuid: string;
    };

    export type RemoveChatResult = {
      outcome: "removed";
    };

    export const zodRemoveChatErrorReason = z.enum(["chatNotFound"]);

    export type ListChatsPayload = {
      agentUuid: string;
      releaseUuid?: string;
      triggerType?: AiTypes.ChatTrigger["type"];
      limit?: number;
      offset?: number;
    };

    export type ListChatsResult = {
      chats: Chat[];
    };

    export type ListMessagesPayload = {
      chatUuid: string;
      limit?: number;
      offset?: number;
    };

    export type ListMessagesResult = {
      messages: Message[];
    };

    export const zodRemoveMessageErrorReason = z.enum([
      "messageNotFound",
      "messageNotUser",
    ]);

    export type UpsertVotePayload = {
      chatUuid: string;
      messageUuid: string;
      isUpvoted: boolean;
    };

    export type UpsertVoteResult = {
      vote: Vote;
    };

    export type ListVotesPayload = {
      chatUuid: string;
    };

    export type ListVotesResult = {
      votes: Vote[];
    };

    export type GetDocumentResult = {
      document: Document;
    };

    export type ListDocumentsPayload = {
      uuid: string;
      limit?: number;
      offset?: number;
    };

    export type ListDocumentsResult = {
      documents: Document[];
    };

    export type CreateDocumentPayload = {
      uuid: string;
      kind: AiTypes.DocumentKind;
      title: string;
      content: string;
    };

    export type CreateDocumentResult = {
      document: Document;
    };

    export type GetMcpServerResult = {
      mcpServer: McpServer;
    };

    export type CreateMcpServerPayload = {
      name: string;
      tools?: AiTypes.Tool[];
    };

    export type CreateMcpServerResult = {
      mcpServer: McpServer;
    };

    export type AllMcpServersResult = {
      mcpServers: McpServer[];
    };

    export type UpdateMcpServerPayload = {
      uuid: string;
      name?: string;
      tools?: AiTypes.Tool[];
    };

    export const zodUpdateMcpServerErrorReason = z.enum(["mcpServerNotFound"]);

    export type UpdateMcpServerResult = {
      mcpServer: McpServer;
    };

    export const zodRemoveMcpServerErrorReason = z.enum(["mcpServerNotFound"]);
  }

  export const mcpServerKeys = {
    all: (workspaceUuid: string) => ["batches", workspaceUuid] as const,
  };

  export const agentKeys = {
    all: (workspaceUuid: string) => ["agents", workspaceUuid] as const,
  };

  export const chatKeys = {
    all: (workspaceUuid: string) => ["chats", workspaceUuid] as const,
    get: (workspaceUuid: string, uuid: string) =>
      [...chatKeys.all(workspaceUuid), uuid] as const,
    list: (workspaceUuid: string, payload: Api.ListChatsPayload) =>
      [...chatKeys.all(workspaceUuid), payload] as const,
  };

  export const messageKeys = {
    all: (workspaceUuid: string) => ["messages", workspaceUuid] as const,
    list: (workspaceUuid: string, payload: Api.ListMessagesPayload) =>
      [...messageKeys.all(workspaceUuid), payload] as const,
  };

  export const voteKeys = {
    all: (workspaceUuid: string) => ["votes", workspaceUuid] as const,
    list: (workspaceUuid: string, payload: Api.ListVotesPayload) =>
      [...voteKeys.all(workspaceUuid), payload] as const,
  };

  export const releaseKeys = {
    all: (workspaceUuid: string) => ["releases", workspaceUuid] as const,
    get: (workspaceUuid: string, uuid: string) =>
      [...releaseKeys.all(workspaceUuid), uuid] as const,
    getDraft: (workspaceUuid: string, workflowUuid: string) =>
      [...releaseKeys.all(workspaceUuid), workflowUuid] as const,
    list: (workspaceUuid: string, payload: Api.ListReleasesPayload) =>
      [...releaseKeys.all(workspaceUuid), payload] as const,
  };

  export const documentKeys = {
    all: (workspaceUuid: string) => ["documents", workspaceUuid] as const,
    get: (workspaceUuid: string, uuid: string) =>
      [...documentKeys.all(workspaceUuid), uuid] as const,
    list: (workspaceUuid: string, payload: Api.ListDocumentsPayload) =>
      [...documentKeys.all(workspaceUuid), payload] as const,
  };
}

export interface AiSlice {
  mcpServers: Ai.McpServer[];
  setMcpServers: (workflows: Ai.McpServer[]) => void;
  agents: Ai.Agent[];
  setAgents: (agents: Ai.Agent[]) => void;
}

export const createAiSlice: StateCreator<AiSlice, [], [], AiSlice> = (set) => ({
  mcpServers: [],
  setMcpServers: (mcpServers) => {
    set((state) => ({
      ...state,
      mcpServers,
    }));
  },
  agents: [],
  setAgents: (agents) => {
    set((state) => ({
      ...state,
      agents,
    }));
  },
});
